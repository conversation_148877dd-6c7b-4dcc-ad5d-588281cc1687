# math_server.py
from mcp.server.fastmcp import FastMCP
import requests
from typing import Optional, Dict, Any

mcp = FastMCP("Entity", log_level="DEBUG", port=8002)

@mcp.tool()
def get_entity_info(company_alias: str) -> str:
    """获取企业详细信息
    
    Args:
        company_alias: 企业简称
        
    Returns:
        str: 企业详细信息
    """
    base_url = "http://122.9.11.55:8084/aliasSearch/"
    url = f"{base_url}{company_alias}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()  # 检查请求是否成功
        return response.text
    except requests.RequestException as e:
        return f"获取企业信息失败: {str(e)}"

@mcp.tool()
def search_company_info(
    credit_code: str,
    score_gte: float = 0.0,
    publish_time_gte: str = "",
    publish_time_lte: str = "",
    tendency: str = "",
    index: str = "default"
) -> Optional[Dict[str, Any]]:
    """根据企业信用代码和关键词进行检索
    
    Args:
        credit_code: 企业信用代码
        score_gte: 相关性分数阈值
        publish_time_gte: 发布时间起始
        publish_time_lte: 发布时间结束
        tendency: 倾向性关键词
        index: 索引名称
        
    Returns:
        Optional[Dict[str, Any]]: 检索结果，如果失败则返回None
    """
    base_url = "http://122.9.11.55:8084"  # 请根据实际情况修改基础URL
    
    # 构建查询条件
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "nested": {
                            "path": "correlationOrg",
                            "query": {
                                "bool": {
                                    "must": [
                                        {
                                            "term": {
                                                "correlationOrg.creditCode.keyword": {
                                                    "value": credit_code
                                                }
                                            }
                                        },
                                        {
                                            "range": {
                                                "correlationOrg.score": {
                                                    "gte": score_gte
                                                }
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    }
                ]
            }
        },
        "_source": ['site_url', 'title', 'publishtime', 'tag', 'url', "content"]
    }
    
    # 添加时间范围条件
    if publish_time_gte or publish_time_lte:
        time_range = {}
        if publish_time_gte:
            time_range["gte"] = publish_time_gte
        if publish_time_lte:
            time_range["lte"] = publish_time_lte
        if time_range:
            query["query"]["bool"]["must"].append({
                "range": {
                    "publishtime": time_range
                }
            })
    
    # 添加倾向性关键词条件
    if tendency:
        query["query"]["bool"]["must"].append({
            "match": {
                "tendency": tendency
            }
        })
    
    try:
        url = f"{base_url}/v1/api/getDataIn1358"
        response = requests.post(url, json={"index": index, "query": query})
        response.raise_for_status()
        
        data = response.json()
        if data and data.get('result') == 'ok':
            return data.get('message')
        return None
        
    except requests.RequestException as e:
        print(f"检索失败: {str(e)}")
        return None

if __name__ == "__main__":
    mcp.run(transport="sse")
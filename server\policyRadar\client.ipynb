{"cells": [{"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Create server parameters for stdio connection\n", "from mcp import ClientSession, StdioServerParameters\n", "from mcp.client.stdio import stdio_client\n", "from langchain_mcp_adapters.tools import load_mcp_tools\n", "from langgraph.prebuilt import create_react_agent\n", "from langchain_openai import ChatOpenAI\n", "import os"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["LLM_MODEL=\"Qwen/Qwen2.5-72B-Instruct-128K\"\n", "\n", "LLM_TEMPERATURE=0.1\n", "LLM_API_BASE=\"https://api.siliconflow.cn/v1\"\n", "#OPENAI_API_KEY=sk-8a7b594b6ec14eda83b68c2bb67500ac\n", "OPENAI_API_KEY=\"sk-jexorjtcpbtmhtnxyqcvlzbkxjhsqaqzvkrniotauxuzjlmn\"\n", "\n", "os.environ[\"LLM_MODEL\"] = LLM_MODEL\n", "os.environ[\"OPENAI_API_KEY\"] = OPENAI_API_KEY\n", "os.environ[\"LLM_API_BASE\"] = LLM_API_BASE\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["model = ChatOpenAI(\n", "        model=os.getenv(\"LLM_MODEL\"),\n", "        openai_api_key=os.getenv(\"OPENAI_API_KEY\"),\n", "        openai_api_base=os.getenv(\"LLM_API_BASE\"),\n", "    )"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["curl https://api.siliconflow.cn/v1/chat/completions \\\n", " -H \"Content-Type: application/json\" \\\n", " -H \"Authorization: sk-vkmolqivosbciwqhybrjqcpgopurxdmgmbkiliwznxdgunqn\" \\\n", " -d '{ \"model\": \"Qwen/Qwen3-32B\", \"messages\": [ { \"role\": \"system\", \"content\": \"你是一个微博博主\" }, { \"role\": \"user\", \"content\": \"写一条关于德云社的段子微博\" } ] }'"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["消息流程:\n", "消息 1:\n", "  类型: HumanMessage\n", "  内容: 三乘以四是多少?\n", "\n", "消息 2:\n", "  类型: AIMessage\n", "  内容: \n", "\n", "消息 3:\n", "  类型: ToolMessage\n", "  内容: 12\n", "  工具名称: multiply\n", "  工具调用ID: 0196cbe0dd4e23cea6d73eaef0458a6f\n", "\n", "消息 4:\n", "  类型: AIMessage\n", "  内容: 三乘以四的结果是12。\n", "\n", "最终结果: 三乘以四的结果是12。\n"]}], "source": ["curl http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen3_32b_2/v1/chat/completions \\\n", " -H \"Content-Type: application/json\" \\\n", " -H \"Authorization: ZmYzMmQ1N2E2YWZjMzcwN2Y3NTBjNmQzYjk3Njk3ZWY4Njk5MWQ0ZQ==\" \\\n", " -d '{ \"model\": \"Qwen3-32B\", \"messages\": [ { \"role\": \"system\", \"content\": \"你是一个微博博主\" }, { \"role\": \"user\", \"content\": \"写一条关于德云社的段子微博\" } ] }'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# llm_service_url = \"http://1125504365556804.cn-beijing.pai-eas.aliyuncs.com/api/predict/qwen3_32b_2/v1\"\n", "# llm_api_key = \"ZmYzMmQ1N2E2YWZjMzcwN2Y3NTBjNmQzYjk3Njk3ZWY4Njk5MWQ0ZQ==\"\n", "# OPENAI_MODEL = \"Qwen3-32B\""]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from langchain_mcp_adapters.client import MultiServerMCPClient"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"ename": "CancelledError", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mCancelledError\u001b[0m                            Traceback (most recent call last)", "Cell \u001b[0;32mIn[20], line 10\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mwith\u001b[39;00m MultiServerMCPClient(\n\u001b[1;32m      2\u001b[0m     {\n\u001b[1;32m      3\u001b[0m          \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpolicyRadar\u001b[39m\u001b[38;5;124m\"\u001b[39m: {\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m      7\u001b[0m     }\n\u001b[1;32m      8\u001b[0m ) \u001b[38;5;28;01mas\u001b[39;00m client:\n\u001b[1;32m      9\u001b[0m     agent \u001b[38;5;241m=\u001b[39m create_react_agent(model, client\u001b[38;5;241m.\u001b[39mget_tools())\n\u001b[0;32m---> 10\u001b[0m     entity_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m agent\u001b[38;5;241m.\u001b[39mainvoke({\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m山东地区的最新政策\u001b[39m\u001b[38;5;124m\"\u001b[39m})\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langgraph/pregel/__init__.py:2892\u001b[0m, in \u001b[0;36mPregel.ainvoke\u001b[0;34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, checkpoint_during, debug, **kwargs)\u001b[0m\n\u001b[1;32m   2889\u001b[0m chunks: \u001b[38;5;28mlist\u001b[39m[Union[\u001b[38;5;28mdict\u001b[39m[\u001b[38;5;28mstr\u001b[39m, Any], Any]] \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m   2890\u001b[0m interrupts: \u001b[38;5;28mlist\u001b[39m[Interrupt] \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m-> 2892\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m chunk \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mastream(\n\u001b[1;32m   2893\u001b[0m     \u001b[38;5;28minput\u001b[39m,\n\u001b[1;32m   2894\u001b[0m     config,\n\u001b[1;32m   2895\u001b[0m     stream_mode\u001b[38;5;241m=\u001b[39mstream_mode,\n\u001b[1;32m   2896\u001b[0m     output_keys\u001b[38;5;241m=\u001b[39moutput_keys,\n\u001b[1;32m   2897\u001b[0m     interrupt_before\u001b[38;5;241m=\u001b[39minterrupt_before,\n\u001b[1;32m   2898\u001b[0m     interrupt_after\u001b[38;5;241m=\u001b[39minterrupt_after,\n\u001b[1;32m   2899\u001b[0m     checkpoint_during\u001b[38;5;241m=\u001b[39mcheckpoint_during,\n\u001b[1;32m   2900\u001b[0m     debug\u001b[38;5;241m=\u001b[39mdebug,\n\u001b[1;32m   2901\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m   2902\u001b[0m ):\n\u001b[1;32m   2903\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m stream_mode \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalues\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m   2904\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[1;32m   2905\u001b[0m             \u001b[38;5;28misinstance\u001b[39m(chunk, \u001b[38;5;28mdict\u001b[39m)\n\u001b[1;32m   2906\u001b[0m             \u001b[38;5;129;01mand\u001b[39;00m (ints \u001b[38;5;241m:=\u001b[39m chunk\u001b[38;5;241m.\u001b[39mget(INTERRUPT)) \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   2907\u001b[0m         ):\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langgraph/pregel/__init__.py:2759\u001b[0m, in \u001b[0;36mPregel.astream\u001b[0;34m(self, input, config, stream_mode, output_keys, interrupt_before, interrupt_after, checkpoint_during, debug, subgraphs)\u001b[0m\n\u001b[1;32m   2753\u001b[0m \u001b[38;5;66;03m# Similarly to Bulk Synchronous Parallel / Pregel model\u001b[39;00m\n\u001b[1;32m   2754\u001b[0m \u001b[38;5;66;03m# computation proceeds in steps, while there are channel updates\u001b[39;00m\n\u001b[1;32m   2755\u001b[0m \u001b[38;5;66;03m# channel updates from step N are only visible in step N+1\u001b[39;00m\n\u001b[1;32m   2756\u001b[0m \u001b[38;5;66;03m# channels are guaranteed to be immutable for the duration of the step,\u001b[39;00m\n\u001b[1;32m   2757\u001b[0m \u001b[38;5;66;03m# with channel updates applied only at the transition between steps\u001b[39;00m\n\u001b[1;32m   2758\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m loop\u001b[38;5;241m.\u001b[39mtick(input_keys\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_channels):\n\u001b[0;32m-> 2759\u001b[0m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mfor\u001b[39;00m _ \u001b[38;5;129;01min\u001b[39;00m runner\u001b[38;5;241m.\u001b[39matick(\n\u001b[1;32m   2760\u001b[0m         loop\u001b[38;5;241m.\u001b[39mtasks\u001b[38;5;241m.\u001b[39mvalues(),\n\u001b[1;32m   2761\u001b[0m         timeout\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstep_timeout,\n\u001b[1;32m   2762\u001b[0m         retry_policy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mretry_policy,\n\u001b[1;32m   2763\u001b[0m         get_waiter\u001b[38;5;241m=\u001b[39mget_waiter,\n\u001b[1;32m   2764\u001b[0m     ):\n\u001b[1;32m   2765\u001b[0m         \u001b[38;5;66;03m# emit output\u001b[39;00m\n\u001b[1;32m   2766\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m o \u001b[38;5;129;01min\u001b[39;00m output():\n\u001b[1;32m   2767\u001b[0m             \u001b[38;5;28;01myield\u001b[39;00m o\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langgraph/pregel/runner.py:283\u001b[0m, in \u001b[0;36mPregelRunner.atick\u001b[0;34m(self, tasks, reraise, timeout, retry_policy, get_waiter)\u001b[0m\n\u001b[1;32m    281\u001b[0m t \u001b[38;5;241m=\u001b[39m tasks[\u001b[38;5;241m0\u001b[39m]\n\u001b[1;32m    282\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 283\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m arun_with_retry(\n\u001b[1;32m    284\u001b[0m         t,\n\u001b[1;32m    285\u001b[0m         retry_policy,\n\u001b[1;32m    286\u001b[0m         stream\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muse_astream,\n\u001b[1;32m    287\u001b[0m         configurable\u001b[38;5;241m=\u001b[39m{\n\u001b[1;32m    288\u001b[0m             CONFIG_KEY_CALL: partial(\n\u001b[1;32m    289\u001b[0m                 _acall,\n\u001b[1;32m    290\u001b[0m                 weakref\u001b[38;5;241m.\u001b[39mref(t),\n\u001b[1;32m    291\u001b[0m                 stream\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39muse_astream,\n\u001b[1;32m    292\u001b[0m                 retry\u001b[38;5;241m=\u001b[39mretry_policy,\n\u001b[1;32m    293\u001b[0m                 futures\u001b[38;5;241m=\u001b[39mweakref\u001b[38;5;241m.\u001b[39mref(futures),\n\u001b[1;32m    294\u001b[0m                 schedule_task\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mschedule_task,\n\u001b[1;32m    295\u001b[0m                 submit\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msubmit,\n\u001b[1;32m    296\u001b[0m                 reraise\u001b[38;5;241m=\u001b[39mreraise,\n\u001b[1;32m    297\u001b[0m                 loop\u001b[38;5;241m=\u001b[39mloop,\n\u001b[1;32m    298\u001b[0m             ),\n\u001b[1;32m    299\u001b[0m         },\n\u001b[1;32m    300\u001b[0m     )\n\u001b[1;32m    301\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommit(t, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m    302\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langgraph/pregel/retry.py:128\u001b[0m, in \u001b[0;36marun_with_retry\u001b[0;34m(task, retry_policies, stream, configurable)\u001b[0m\n\u001b[1;32m    126\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    127\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 128\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m task\u001b[38;5;241m.\u001b[39mproc\u001b[38;5;241m.\u001b[39mainvoke(task\u001b[38;5;241m.\u001b[39minput, config)\n\u001b[1;32m    129\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ParentCommand \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    130\u001b[0m     ns: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m config[CONF][CONFIG_KEY_CHECKPOINT_NS]\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langgraph/utils/runnable.py:672\u001b[0m, in \u001b[0;36mRunnableSeq.ainvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    670\u001b[0m     \u001b[38;5;66;03m# run in context\u001b[39;00m\n\u001b[1;32m    671\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m set_config_context(config, run) \u001b[38;5;28;01mas\u001b[39;00m context:\n\u001b[0;32m--> 672\u001b[0m         \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m asyncio\u001b[38;5;241m.\u001b[39mcreate_task(\n\u001b[1;32m    673\u001b[0m             step\u001b[38;5;241m.\u001b[39mainvoke(\u001b[38;5;28minput\u001b[39m, config, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs), context\u001b[38;5;241m=\u001b[39mcontext\n\u001b[1;32m    674\u001b[0m         )\n\u001b[1;32m    675\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    676\u001b[0m     \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m step\u001b[38;5;241m.\u001b[39mainvoke(\u001b[38;5;28minput\u001b[39m, config, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langgraph/utils/runnable.py:431\u001b[0m, in \u001b[0;36mRunnableCallable.ainvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m    429\u001b[0m         run \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    430\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m set_config_context(child_config, run) \u001b[38;5;28;01mas\u001b[39;00m context:\n\u001b[0;32m--> 431\u001b[0m         ret \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m asyncio\u001b[38;5;241m.\u001b[39mcreate_task(coro, context\u001b[38;5;241m=\u001b[39mcontext)\n\u001b[1;32m    432\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    433\u001b[0m     ret \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m coro\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langgraph/prebuilt/chat_agent_executor.py:763\u001b[0m, in \u001b[0;36mcreate_react_agent.<locals>.acall_model\u001b[0;34m(state, config)\u001b[0m\n\u001b[1;32m    761\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21macall_model\u001b[39m(state: StateSchema, config: RunnableConfig) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m StateSchema:\n\u001b[1;32m    762\u001b[0m     state \u001b[38;5;241m=\u001b[39m _get_model_input_state(state)\n\u001b[0;32m--> 763\u001b[0m     response \u001b[38;5;241m=\u001b[39m cast(AIMessage, \u001b[38;5;28;01mawait\u001b[39;00m model_runnable\u001b[38;5;241m.\u001b[39mainvoke(state, config))\n\u001b[1;32m    764\u001b[0m     \u001b[38;5;66;03m# add agent name to the AIMessage\u001b[39;00m\n\u001b[1;32m    765\u001b[0m     response\u001b[38;5;241m.\u001b[39mname \u001b[38;5;241m=\u001b[39m name\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langchain_core/runnables/base.py:3066\u001b[0m, in \u001b[0;36mRunnableSequence.ainvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m   3064\u001b[0m     part \u001b[38;5;241m=\u001b[39m functools\u001b[38;5;241m.\u001b[39mpartial(step\u001b[38;5;241m.\u001b[39mainvoke, \u001b[38;5;28minput\u001b[39m, config)\n\u001b[1;32m   3065\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m asyncio_accepts_context():\n\u001b[0;32m-> 3066\u001b[0m     \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m asyncio\u001b[38;5;241m.\u001b[39mcreate_task(part(), context\u001b[38;5;241m=\u001b[39mcontext)  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[1;32m   3067\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   3068\u001b[0m     \u001b[38;5;28minput\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m asyncio\u001b[38;5;241m.\u001b[39mcreate_task(part())\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langchain_core/runnables/base.py:5370\u001b[0m, in \u001b[0;36mRunnableBindingBase.ainvoke\u001b[0;34m(self, input, config, **kwargs)\u001b[0m\n\u001b[1;32m   5364\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mainvoke\u001b[39m(\n\u001b[1;32m   5365\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   5366\u001b[0m     \u001b[38;5;28minput\u001b[39m: Input,\n\u001b[1;32m   5367\u001b[0m     config: Optional[RunnableConfig] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   5368\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Optional[Any],\n\u001b[1;32m   5369\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Output:\n\u001b[0;32m-> 5370\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbound\u001b[38;5;241m.\u001b[39mainvoke(\n\u001b[1;32m   5371\u001b[0m         \u001b[38;5;28minput\u001b[39m,\n\u001b[1;32m   5372\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_merge_configs(config),\n\u001b[1;32m   5373\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m{\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mkwargs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs},\n\u001b[1;32m   5374\u001b[0m     )\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langchain_core/language_models/chat_models.py:328\u001b[0m, in \u001b[0;36mBaseChatModel.ainvoke\u001b[0;34m(self, input, config, stop, **kwargs)\u001b[0m\n\u001b[1;32m    319\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mainvoke\u001b[39m(\n\u001b[1;32m    320\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    321\u001b[0m     \u001b[38;5;28minput\u001b[39m: LanguageModelInput,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    325\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any,\n\u001b[1;32m    326\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m BaseMessage:\n\u001b[1;32m    327\u001b[0m     config \u001b[38;5;241m=\u001b[39m ensure_config(config)\n\u001b[0;32m--> 328\u001b[0m     llm_result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39magenerate_prompt(\n\u001b[1;32m    329\u001b[0m         [\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_convert_input(\u001b[38;5;28minput\u001b[39m)],\n\u001b[1;32m    330\u001b[0m         stop\u001b[38;5;241m=\u001b[39mstop,\n\u001b[1;32m    331\u001b[0m         callbacks\u001b[38;5;241m=\u001b[39mconfig\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcallbacks\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m    332\u001b[0m         tags\u001b[38;5;241m=\u001b[39mconfig\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtags\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m    333\u001b[0m         metadata\u001b[38;5;241m=\u001b[39mconfig\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmetadata\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m    334\u001b[0m         run_name\u001b[38;5;241m=\u001b[39mconfig\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrun_name\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m    335\u001b[0m         run_id\u001b[38;5;241m=\u001b[39mconfig\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrun_id\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[1;32m    336\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m    337\u001b[0m     )\n\u001b[1;32m    338\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ChatGeneration, llm_result\u001b[38;5;241m.\u001b[39mgenerations[\u001b[38;5;241m0\u001b[39m][\u001b[38;5;241m0\u001b[39m])\u001b[38;5;241m.\u001b[39mmessage\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langchain_core/language_models/chat_models.py:853\u001b[0m, in \u001b[0;36mBaseChatModel.agenerate_prompt\u001b[0;34m(self, prompts, stop, callbacks, **kwargs)\u001b[0m\n\u001b[1;32m    845\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21magenerate_prompt\u001b[39m(\n\u001b[1;32m    846\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    847\u001b[0m     prompts: \u001b[38;5;28mlist\u001b[39m[PromptValue],\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    850\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any,\n\u001b[1;32m    851\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m LLMResult:\n\u001b[1;32m    852\u001b[0m     prompt_messages \u001b[38;5;241m=\u001b[39m [p\u001b[38;5;241m.\u001b[39mto_messages() \u001b[38;5;28;01mfor\u001b[39;00m p \u001b[38;5;129;01min\u001b[39;00m prompts]\n\u001b[0;32m--> 853\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39magenerate(\n\u001b[1;32m    854\u001b[0m         prompt_messages, stop\u001b[38;5;241m=\u001b[39mstop, callbacks\u001b[38;5;241m=\u001b[39mcallbacks, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs\n\u001b[1;32m    855\u001b[0m     )\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langchain_core/language_models/chat_models.py:779\u001b[0m, in \u001b[0;36mBaseChatModel.agenerate\u001b[0;34m(self, messages, stop, callbacks, tags, metadata, run_name, run_id, **kwargs)\u001b[0m\n\u001b[1;32m    759\u001b[0m callback_manager \u001b[38;5;241m=\u001b[39m AsyncCallbackManager\u001b[38;5;241m.\u001b[39mconfigure(\n\u001b[1;32m    760\u001b[0m     callbacks,\n\u001b[1;32m    761\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcallbacks,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    766\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmetadata,\n\u001b[1;32m    767\u001b[0m )\n\u001b[1;32m    769\u001b[0m run_managers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01ma<PERSON>t\u001b[39;00m callback_manager\u001b[38;5;241m.\u001b[39mon_chat_model_start(\n\u001b[1;32m    770\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_serialized,\n\u001b[1;32m    771\u001b[0m     messages,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    776\u001b[0m     run_id\u001b[38;5;241m=\u001b[39mrun_id,\n\u001b[1;32m    777\u001b[0m )\n\u001b[0;32m--> 779\u001b[0m results \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m asyncio\u001b[38;5;241m.\u001b[39mgather(\n\u001b[1;32m    780\u001b[0m     \u001b[38;5;241m*\u001b[39m[\n\u001b[1;32m    781\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_agenerate_with_cache(\n\u001b[1;32m    782\u001b[0m             m,\n\u001b[1;32m    783\u001b[0m             stop\u001b[38;5;241m=\u001b[39mstop,\n\u001b[1;32m    784\u001b[0m             run_manager\u001b[38;5;241m=\u001b[39mrun_managers[i] \u001b[38;5;28;01mif\u001b[39;00m run_managers \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    785\u001b[0m             \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m    786\u001b[0m         )\n\u001b[1;32m    787\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m i, m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(messages)\n\u001b[1;32m    788\u001b[0m     ],\n\u001b[1;32m    789\u001b[0m     return_exceptions\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[1;32m    790\u001b[0m )\n\u001b[1;32m    791\u001b[0m exceptions \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m    792\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i, res \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(results):\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langchain_core/language_models/chat_models.py:981\u001b[0m, in \u001b[0;36mBaseChatModel._agenerate_with_cache\u001b[0;34m(self, messages, stop, run_manager, **kwargs)\u001b[0m\n\u001b[1;32m    979\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    980\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m inspect\u001b[38;5;241m.\u001b[39msignature(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_agenerate)\u001b[38;5;241m.\u001b[39mparameters\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrun_manager\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m--> 981\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_agenerate(\n\u001b[1;32m    982\u001b[0m             messages, stop\u001b[38;5;241m=\u001b[39mstop, run_manager\u001b[38;5;241m=\u001b[39mrun_manager, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs\n\u001b[1;32m    983\u001b[0m         )\n\u001b[1;32m    984\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    985\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_agenerate(messages, stop\u001b[38;5;241m=\u001b[39mstop, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/langchain_openai/chat_models/base.py:1157\u001b[0m, in \u001b[0;36mBaseChatOpenAI._agenerate\u001b[0;34m(self, messages, stop, run_manager, **kwargs)\u001b[0m\n\u001b[1;32m   1155\u001b[0m     generation_info \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mheaders\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mdict\u001b[39m(raw_response\u001b[38;5;241m.\u001b[39mheaders)}\n\u001b[1;32m   1156\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1157\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39masync_client\u001b[38;5;241m.\u001b[39mcreate(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mpayload)\n\u001b[1;32m   1158\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m run_in_executor(\n\u001b[1;32m   1159\u001b[0m     \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_create_chat_result, response, generation_info\n\u001b[1;32m   1160\u001b[0m )\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/openai/resources/chat/completions/completions.py:2028\u001b[0m, in \u001b[0;36mAsyncCompletions.create\u001b[0;34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, web_search_options, extra_headers, extra_query, extra_body, timeout)\u001b[0m\n\u001b[1;32m   1985\u001b[0m \u001b[38;5;129m@required_args\u001b[39m([\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m], [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[1;32m   1986\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mcreate\u001b[39m(\n\u001b[1;32m   1987\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   2025\u001b[0m     timeout: \u001b[38;5;28mfloat\u001b[39m \u001b[38;5;241m|\u001b[39m httpx\u001b[38;5;241m.\u001b[39mTimeout \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m|\u001b[39m NotGiven \u001b[38;5;241m=\u001b[39m NOT_GIVEN,\n\u001b[1;32m   2026\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ChatCompletion \u001b[38;5;241m|\u001b[39m AsyncStream[ChatCompletionChunk]:\n\u001b[1;32m   2027\u001b[0m     validate_response_format(response_format)\n\u001b[0;32m-> 2028\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_post(\n\u001b[1;32m   2029\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/chat/completions\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   2030\u001b[0m         body\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mawait\u001b[39;00m async_maybe_transform(\n\u001b[1;32m   2031\u001b[0m             {\n\u001b[1;32m   2032\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: messages,\n\u001b[1;32m   2033\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m: model,\n\u001b[1;32m   2034\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m\"\u001b[39m: audio,\n\u001b[1;32m   2035\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfrequency_penalty\u001b[39m\u001b[38;5;124m\"\u001b[39m: frequency_penalty,\n\u001b[1;32m   2036\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunction_call\u001b[39m\u001b[38;5;124m\"\u001b[39m: function_call,\n\u001b[1;32m   2037\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfunctions\u001b[39m\u001b[38;5;124m\"\u001b[39m: functions,\n\u001b[1;32m   2038\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlogit_bias\u001b[39m\u001b[38;5;124m\"\u001b[39m: logit_bias,\n\u001b[1;32m   2039\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlogprobs\u001b[39m\u001b[38;5;124m\"\u001b[39m: logprobs,\n\u001b[1;32m   2040\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmax_completion_tokens\u001b[39m\u001b[38;5;124m\"\u001b[39m: max_completion_tokens,\n\u001b[1;32m   2041\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmax_tokens\u001b[39m\u001b[38;5;124m\"\u001b[39m: max_tokens,\n\u001b[1;32m   2042\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmetadata\u001b[39m\u001b[38;5;124m\"\u001b[39m: metadata,\n\u001b[1;32m   2043\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodalities\u001b[39m\u001b[38;5;124m\"\u001b[39m: modalities,\n\u001b[1;32m   2044\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mn\u001b[39m\u001b[38;5;124m\"\u001b[39m: n,\n\u001b[1;32m   2045\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mparallel_tool_calls\u001b[39m\u001b[38;5;124m\"\u001b[39m: parallel_tool_calls,\n\u001b[1;32m   2046\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprediction\u001b[39m\u001b[38;5;124m\"\u001b[39m: prediction,\n\u001b[1;32m   2047\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpresence_penalty\u001b[39m\u001b[38;5;124m\"\u001b[39m: presence_penalty,\n\u001b[1;32m   2048\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mreasoning_effort\u001b[39m\u001b[38;5;124m\"\u001b[39m: reasoning_effort,\n\u001b[1;32m   2049\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse_format\u001b[39m\u001b[38;5;124m\"\u001b[39m: response_format,\n\u001b[1;32m   2050\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mseed\u001b[39m\u001b[38;5;124m\"\u001b[39m: seed,\n\u001b[1;32m   2051\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mservice_tier\u001b[39m\u001b[38;5;124m\"\u001b[39m: service_tier,\n\u001b[1;32m   2052\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstop\u001b[39m\u001b[38;5;124m\"\u001b[39m: stop,\n\u001b[1;32m   2053\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstore\u001b[39m\u001b[38;5;124m\"\u001b[39m: store,\n\u001b[1;32m   2054\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream\u001b[39m\u001b[38;5;124m\"\u001b[39m: stream,\n\u001b[1;32m   2055\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstream_options\u001b[39m\u001b[38;5;124m\"\u001b[39m: stream_options,\n\u001b[1;32m   2056\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtemperature\u001b[39m\u001b[38;5;124m\"\u001b[39m: temperature,\n\u001b[1;32m   2057\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtool_choice\u001b[39m\u001b[38;5;124m\"\u001b[39m: tool_choice,\n\u001b[1;32m   2058\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtools\u001b[39m\u001b[38;5;124m\"\u001b[39m: tools,\n\u001b[1;32m   2059\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtop_logprobs\u001b[39m\u001b[38;5;124m\"\u001b[39m: top_logprobs,\n\u001b[1;32m   2060\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtop_p\u001b[39m\u001b[38;5;124m\"\u001b[39m: top_p,\n\u001b[1;32m   2061\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124muser\u001b[39m\u001b[38;5;124m\"\u001b[39m: user,\n\u001b[1;32m   2062\u001b[0m                 \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mweb_search_options\u001b[39m\u001b[38;5;124m\"\u001b[39m: web_search_options,\n\u001b[1;32m   2063\u001b[0m             },\n\u001b[1;32m   2064\u001b[0m             completion_create_params\u001b[38;5;241m.\u001b[39mCompletionCreateParamsStreaming\n\u001b[1;32m   2065\u001b[0m             \u001b[38;5;28;01mif\u001b[39;00m stream\n\u001b[1;32m   2066\u001b[0m             \u001b[38;5;28;01melse\u001b[39;00m completion_create_params\u001b[38;5;241m.\u001b[39mCompletionCreateParamsNonStreaming,\n\u001b[1;32m   2067\u001b[0m         ),\n\u001b[1;32m   2068\u001b[0m         options\u001b[38;5;241m=\u001b[39mmake_request_options(\n\u001b[1;32m   2069\u001b[0m             extra_headers\u001b[38;5;241m=\u001b[39mextra_headers, extra_query\u001b[38;5;241m=\u001b[39mextra_query, extra_body\u001b[38;5;241m=\u001b[39mextra_body, timeout\u001b[38;5;241m=\u001b[39mtimeout\n\u001b[1;32m   2070\u001b[0m         ),\n\u001b[1;32m   2071\u001b[0m         cast_to\u001b[38;5;241m=\u001b[39mChatCompletion,\n\u001b[1;32m   2072\u001b[0m         stream\u001b[38;5;241m=\u001b[39mstream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m   2073\u001b[0m         stream_cls\u001b[38;5;241m=\u001b[39mAsyncStream[ChatCompletionChunk],\n\u001b[1;32m   2074\u001b[0m     )\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/openai/_base_client.py:1742\u001b[0m, in \u001b[0;36mAsyncAPIClient.post\u001b[0;34m(self, path, cast_to, body, files, options, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1728\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mpost\u001b[39m(\n\u001b[1;32m   1729\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1730\u001b[0m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1737\u001b[0m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_AsyncStreamT] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m,\n\u001b[1;32m   1738\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ResponseT \u001b[38;5;241m|\u001b[39m _AsyncStreamT:\n\u001b[1;32m   1739\u001b[0m     opts \u001b[38;5;241m=\u001b[39m FinalRequestOptions\u001b[38;5;241m.\u001b[39mconstruct(\n\u001b[1;32m   1740\u001b[0m         method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpost\u001b[39m\u001b[38;5;124m\"\u001b[39m, url\u001b[38;5;241m=\u001b[39mpath, json_data\u001b[38;5;241m=\u001b[39mbody, files\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mawait\u001b[39;00m async_to_httpx_files(files), \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39moptions\n\u001b[1;32m   1741\u001b[0m     )\n\u001b[0;32m-> 1742\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest(cast_to, opts, stream\u001b[38;5;241m=\u001b[39mstream, stream_cls\u001b[38;5;241m=\u001b[39mstream_cls)\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/openai/_base_client.py:1484\u001b[0m, in \u001b[0;36mAsyncAPIClient.request\u001b[0;34m(self, cast_to, options, stream, stream_cls)\u001b[0m\n\u001b[1;32m   1482\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1483\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1484\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_client\u001b[38;5;241m.\u001b[39msend(\n\u001b[1;32m   1485\u001b[0m         request,\n\u001b[1;32m   1486\u001b[0m         stream\u001b[38;5;241m=\u001b[39mstream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_should_stream_response_body(request\u001b[38;5;241m=\u001b[39mrequest),\n\u001b[1;32m   1487\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m   1488\u001b[0m     )\n\u001b[1;32m   1489\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m httpx\u001b[38;5;241m.\u001b[39mTimeoutException \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m   1490\u001b[0m     log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEncountered httpx.TimeoutException\u001b[39m\u001b[38;5;124m\"\u001b[39m, exc_info\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpx/_client.py:1629\u001b[0m, in \u001b[0;36mAsyncClient.send\u001b[0;34m(self, request, stream, auth, follow_redirects)\u001b[0m\n\u001b[1;32m   1625\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_set_timeout(request)\n\u001b[1;32m   1627\u001b[0m auth \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_build_request_auth(request, auth)\n\u001b[0;32m-> 1629\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_handling_auth(\n\u001b[1;32m   1630\u001b[0m     request,\n\u001b[1;32m   1631\u001b[0m     auth\u001b[38;5;241m=\u001b[39mauth,\n\u001b[1;32m   1632\u001b[0m     follow_redirects\u001b[38;5;241m=\u001b[39mfollow_redirects,\n\u001b[1;32m   1633\u001b[0m     history\u001b[38;5;241m=\u001b[39m[],\n\u001b[1;32m   1634\u001b[0m )\n\u001b[1;32m   1635\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1636\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m stream:\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpx/_client.py:1657\u001b[0m, in \u001b[0;36mAsyncClient._send_handling_auth\u001b[0;34m(self, request, auth, follow_redirects, history)\u001b[0m\n\u001b[1;32m   1654\u001b[0m request \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m auth_flow\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__anext__\u001b[39m()\n\u001b[1;32m   1656\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m-> 1657\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_handling_redirects(\n\u001b[1;32m   1658\u001b[0m         request,\n\u001b[1;32m   1659\u001b[0m         follow_redirects\u001b[38;5;241m=\u001b[39mfollow_redirects,\n\u001b[1;32m   1660\u001b[0m         history\u001b[38;5;241m=\u001b[39mhistory,\n\u001b[1;32m   1661\u001b[0m     )\n\u001b[1;32m   1662\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1663\u001b[0m         \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpx/_client.py:1694\u001b[0m, in \u001b[0;36mAsyncClient._send_handling_redirects\u001b[0;34m(self, request, follow_redirects, history)\u001b[0m\n\u001b[1;32m   1691\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_event_hooks[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrequest\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n\u001b[1;32m   1692\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m hook(request)\n\u001b[0;32m-> 1694\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_send_single_request(request)\n\u001b[1;32m   1695\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m   1696\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_event_hooks[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mresponse\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpx/_client.py:1730\u001b[0m, in \u001b[0;36mAsyncClient._send_single_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m   1725\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[1;32m   1726\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAttempted to send an sync request with an AsyncClient instance.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1727\u001b[0m     )\n\u001b[1;32m   1729\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request\u001b[38;5;241m=\u001b[39mrequest):\n\u001b[0;32m-> 1730\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m transport\u001b[38;5;241m.\u001b[39mhandle_async_request(request)\n\u001b[1;32m   1732\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response\u001b[38;5;241m.\u001b[39mstream, AsyncByteStream)\n\u001b[1;32m   1733\u001b[0m response\u001b[38;5;241m.\u001b[39mrequest \u001b[38;5;241m=\u001b[39m request\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpx/_transports/default.py:394\u001b[0m, in \u001b[0;36mAsyncHTTPTransport.handle_async_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    381\u001b[0m req \u001b[38;5;241m=\u001b[39m httpcore\u001b[38;5;241m.\u001b[39mRequest(\n\u001b[1;32m    382\u001b[0m     method\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mmethod,\n\u001b[1;32m    383\u001b[0m     url\u001b[38;5;241m=\u001b[39mhttpcore\u001b[38;5;241m.\u001b[39mURL(\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    391\u001b[0m     extensions\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mextensions,\n\u001b[1;32m    392\u001b[0m )\n\u001b[1;32m    393\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[0;32m--> 394\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_pool\u001b[38;5;241m.\u001b[39mhandle_async_request(req)\n\u001b[1;32m    396\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp\u001b[38;5;241m.\u001b[39mstream, typing\u001b[38;5;241m.\u001b[39mAsyncIterable)\n\u001b[1;32m    398\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m Response(\n\u001b[1;32m    399\u001b[0m     status_code\u001b[38;5;241m=\u001b[39mresp\u001b[38;5;241m.\u001b[39mstatus,\n\u001b[1;32m    400\u001b[0m     headers\u001b[38;5;241m=\u001b[39mresp\u001b[38;5;241m.\u001b[39mheaders,\n\u001b[1;32m    401\u001b[0m     stream\u001b[38;5;241m=\u001b[39mAsyncResponseStream(resp\u001b[38;5;241m.\u001b[39mstream),\n\u001b[1;32m    402\u001b[0m     extensions\u001b[38;5;241m=\u001b[39mresp\u001b[38;5;241m.\u001b[39mextensions,\n\u001b[1;32m    403\u001b[0m )\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpcore/_async/connection_pool.py:256\u001b[0m, in \u001b[0;36mAsyncConnectionPool.handle_async_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    253\u001b[0m         closing \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_assign_requests_to_connections()\n\u001b[1;32m    255\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_close_connections(closing)\n\u001b[0;32m--> 256\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    258\u001b[0m \u001b[38;5;66;03m# Return the response. Note that in this case we still have to manage\u001b[39;00m\n\u001b[1;32m    259\u001b[0m \u001b[38;5;66;03m# the point at which the response is closed.\u001b[39;00m\n\u001b[1;32m    260\u001b[0m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response\u001b[38;5;241m.\u001b[39mstream, typing\u001b[38;5;241m.\u001b[39mAsyncIterable)\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpcore/_async/connection_pool.py:236\u001b[0m, in \u001b[0;36mAsyncConnectionPool.handle_async_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    232\u001b[0m connection \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m pool_request\u001b[38;5;241m.\u001b[39mwait_for_connection(timeout\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[1;32m    234\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    235\u001b[0m     \u001b[38;5;66;03m# Send the request on the assigned connection.\u001b[39;00m\n\u001b[0;32m--> 236\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m connection\u001b[38;5;241m.\u001b[39mhandle_async_request(\n\u001b[1;32m    237\u001b[0m         pool_request\u001b[38;5;241m.\u001b[39mrequest\n\u001b[1;32m    238\u001b[0m     )\n\u001b[1;32m    239\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ConnectionNotAvailable:\n\u001b[1;32m    240\u001b[0m     \u001b[38;5;66;03m# In some cases a connection may initially be available to\u001b[39;00m\n\u001b[1;32m    241\u001b[0m     \u001b[38;5;66;03m# handle a request, but then become unavailable.\u001b[39;00m\n\u001b[1;32m    242\u001b[0m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m    243\u001b[0m     \u001b[38;5;66;03m# In this case we clear the connection and try again.\u001b[39;00m\n\u001b[1;32m    244\u001b[0m     pool_request\u001b[38;5;241m.\u001b[39mclear_connection()\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpcore/_async/connection.py:101\u001b[0m, in \u001b[0;36mAsyncHTTPConnection.handle_async_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m     99\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    100\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connect_failed \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m--> 101\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[1;32m    103\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connection\u001b[38;5;241m.\u001b[39mhandle_async_request(request)\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpcore/_async/connection.py:78\u001b[0m, in \u001b[0;36mAsyncHTTPConnection.handle_async_request\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m     76\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_request_lock:\n\u001b[1;32m     77\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connection \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m---> 78\u001b[0m         stream \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_connect(request)\n\u001b[1;32m     80\u001b[0m         ssl_object \u001b[38;5;241m=\u001b[39m stream\u001b[38;5;241m.\u001b[39mget_extra_info(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mssl_object\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     81\u001b[0m         http2_negotiated \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m     82\u001b[0m             ssl_object \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m     83\u001b[0m             \u001b[38;5;129;01mand\u001b[39;00m ssl_object\u001b[38;5;241m.\u001b[39mselected_alpn_protocol() \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mh2\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     84\u001b[0m         )\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpcore/_async/connection.py:124\u001b[0m, in \u001b[0;36mAsyncHTTPConnection._connect\u001b[0;34m(self, request)\u001b[0m\n\u001b[1;32m    116\u001b[0m     kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    117\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhost\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_origin\u001b[38;5;241m.\u001b[39mhost\u001b[38;5;241m.\u001b[39mdecode(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mascii\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m    118\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mport\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_origin\u001b[38;5;241m.\u001b[39mport,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    121\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msocket_options\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_socket_options,\n\u001b[1;32m    122\u001b[0m     }\n\u001b[1;32m    123\u001b[0m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mconnect_tcp\u001b[39m\u001b[38;5;124m\"\u001b[39m, logger, request, kwargs) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[0;32m--> 124\u001b[0m         stream \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_network_backend\u001b[38;5;241m.\u001b[39mconnect_tcp(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    125\u001b[0m         trace\u001b[38;5;241m.\u001b[39mreturn_value \u001b[38;5;241m=\u001b[39m stream\n\u001b[1;32m    126\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpcore/_backends/auto.py:31\u001b[0m, in \u001b[0;36mAutoBackend.connect_tcp\u001b[0;34m(self, host, port, timeout, local_address, socket_options)\u001b[0m\n\u001b[1;32m     22\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21mconnect_tcp\u001b[39m(\n\u001b[1;32m     23\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m     24\u001b[0m     host: \u001b[38;5;28mstr\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     28\u001b[0m     socket_options: typing\u001b[38;5;241m.\u001b[39mIterable[SOCKET_OPTION] \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m,\n\u001b[1;32m     29\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m AsyncNetworkStream:\n\u001b[1;32m     30\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_init_backend()\n\u001b[0;32m---> 31\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_backend\u001b[38;5;241m.\u001b[39mconnect_tcp(\n\u001b[1;32m     32\u001b[0m         host,\n\u001b[1;32m     33\u001b[0m         port,\n\u001b[1;32m     34\u001b[0m         timeout\u001b[38;5;241m=\u001b[39mtimeout,\n\u001b[1;32m     35\u001b[0m         local_address\u001b[38;5;241m=\u001b[39mlocal_address,\n\u001b[1;32m     36\u001b[0m         socket_options\u001b[38;5;241m=\u001b[39msocket_options,\n\u001b[1;32m     37\u001b[0m     )\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/httpcore/_backends/anyio.py:115\u001b[0m, in \u001b[0;36mAnyIOBackend.connect_tcp\u001b[0;34m(self, host, port, timeout, local_address, socket_options)\u001b[0m\n\u001b[1;32m    113\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m map_exceptions(exc_map):\n\u001b[1;32m    114\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m anyio\u001b[38;5;241m.\u001b[39mfail_after(timeout):\n\u001b[0;32m--> 115\u001b[0m         stream: anyio\u001b[38;5;241m.\u001b[39mabc\u001b[38;5;241m.\u001b[39mByteStream \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m anyio\u001b[38;5;241m.\u001b[39mconnect_tcp(\n\u001b[1;32m    116\u001b[0m             remote_host\u001b[38;5;241m=\u001b[39mhost,\n\u001b[1;32m    117\u001b[0m             remote_port\u001b[38;5;241m=\u001b[39mport,\n\u001b[1;32m    118\u001b[0m             local_host\u001b[38;5;241m=\u001b[39mlocal_address,\n\u001b[1;32m    119\u001b[0m         )\n\u001b[1;32m    120\u001b[0m         \u001b[38;5;66;03m# By default TCP sockets opened in `asyncio` include TCP_NODELAY.\u001b[39;00m\n\u001b[1;32m    121\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m option \u001b[38;5;129;01min\u001b[39;00m socket_options:\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/anyio/_core/_sockets.py:228\u001b[0m, in \u001b[0;36mconnect_tcp\u001b[0;34m(remote_host, remote_port, local_host, tls, ssl_context, tls_standard_compatible, tls_hostname, happy_eyeballs_delay)\u001b[0m\n\u001b[1;32m    226\u001b[0m oserrors: \u001b[38;5;28mlist\u001b[39m[\u001b[38;5;167;01mOSError\u001b[39;00m] \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m    227\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 228\u001b[0m     \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mwith\u001b[39;00m create_task_group() \u001b[38;5;28;01mas\u001b[39;00m tg:\n\u001b[1;32m    229\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m i, (af, addr) \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(target_addrs):\n\u001b[1;32m    230\u001b[0m             event \u001b[38;5;241m=\u001b[39m Event()\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/anyio/_backends/_asyncio.py:776\u001b[0m, in \u001b[0;36mTaskGroup.__aexit__\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m    772\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m BaseExceptionGroup(\n\u001b[1;32m    773\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124munhandled errors in a TaskGroup\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_exceptions\n\u001b[1;32m    774\u001b[0m         ) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    775\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m exc_val:\n\u001b[0;32m--> 776\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m exc_val\n\u001b[1;32m    777\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mBaseException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    778\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcancel_scope\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__exit__\u001b[39m(\u001b[38;5;28mtype\u001b[39m(exc), exc, exc\u001b[38;5;241m.\u001b[39m__traceback__):\n", "File \u001b[0;32m~/miniforge3/envs/py311/lib/python3.11/site-packages/anyio/_backends/_asyncio.py:744\u001b[0m, in \u001b[0;36mTaskGroup.__aexit__\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m    741\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_on_completed_fut \u001b[38;5;241m=\u001b[39m loop\u001b[38;5;241m.\u001b[39mcreate_future()\n\u001b[1;32m    743\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 744\u001b[0m     \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_on_completed_fut\n\u001b[1;32m    745\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m CancelledError \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    746\u001b[0m     \u001b[38;5;66;03m# Shield the scope against further cancellation attempts,\u001b[39;00m\n\u001b[1;32m    747\u001b[0m     \u001b[38;5;66;03m# as they're not productive (#695)\u001b[39;00m\n\u001b[1;32m    748\u001b[0m     wait_scope\u001b[38;5;241m.\u001b[39mshield \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[0;31mCancelledError\u001b[0m: "]}], "source": ["async with MultiServerMCPClient(\n", "    {\n", "         \"policyRadar\": {\n", "            \"url\": \"http://localhost:8002/sse\",\n", "            \"transport\": \"sse\",\n", "        },\n", "    }\n", ") as client:\n", "    agent = create_react_agent(model, client.get_tools())\n", "    entity_response = await agent.ainvoke({\"messages\": \"山东地区的最新政策\"})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数学计算消息流程:\n", "消息 1:\n", "  类型: HumanMessage\n", "  内容: 山东地区的最新政策\n", "\n", "消息 2:\n", "  类型: AIMessage\n", "  内容: \n", "\n", "消息 3:\n", "  类型: ToolMessage\n", "  内容: {\"results\": [], \"query_details\": {\"size\": 10, \"query\": {\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"\", \"fields\": [\"title\", \"content\", \"summary\", \"laws_title\", \"laws_brief\", \"policy_theme.text\"]}}], \"filter\": [{\"term\": {\"area\": \"\\u5c71\\u4e1c\"}}]}}}}\n", "  工具名称: search_policy_by_keyword\n", "  工具调用ID: 019740c36f4d6f42c6a3ecb5dae7752c\n", "\n", "消息 4:\n", "  类型: AIMessage\n", "  内容: 看起来没有检索到山东地区的最新政策信息。您是否可以提供更具体的关键词以帮助我更准确地查找相关信息？例如，您可以关注某个特定领域的政策，如教育、医疗、环保等。\n", "\n", "实体: 看起来没有检索到山东地区的最新政策信息。您是否可以提供更具体的关键词以帮助我更准确地查找相关信息？例如，您可以关注某个特定领域的政策，如教育、医疗、环保等。\n"]}], "source": ["# 结构化输出数学计算结果\n", "# entity_response = await agent.ainvoke({\"messages\": \"几点了\"})\n", "print(\"数学计算消息流程:\")\n", "for i, message in enumerate(entity_response[\"messages\"]):\n", "    print(f\"消息 {i+1}:\")\n", "    print(f\"  类型: {type(message).__name__}\")\n", "    print(f\"  内容: {message.content}\")\n", "    if hasattr(message, 'name') and message.name:\n", "        print(f\"  工具名称: {message.name}\")\n", "    if hasattr(message, 'tool_call_id') and message.tool_call_id:\n", "        print(f\"  工具调用ID: {message.tool_call_id}\")\n", "    print()\n", "\n", "print(\"实体:\", entity_response[\"messages\"][-1].content)\n", "\n", "# # 结构化输出天气查询结果\n", "# print(\"\\n天气查询消息流程:\")\n", "# for i, message in enumerate(weather_response[\"messages\"]):\n", "#     print(f\"消息 {i+1}:\")\n", "#     print(f\"  类型: {type(message).__name__}\")\n", "#     print(f\"  内容: {message.content}\")\n", "#     if hasattr(message, 'name') and message.name:\n", "#         print(f\"  工具名称: {message.name}\")\n", "#     if hasattr(message, 'tool_call_id') and message.tool_call_id:\n", "#         print(f\"  工具调用ID: {message.tool_call_id}\")\n", "#     print()\n", "\n", "# print(\"天气查询最终结果:\", weather_response[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}
from typing import List, Optional, Dict, Any
import os
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMC<PERSON>
from fastapi import FastAPI
import uvicorn
import requests
import traceback
import logging
import json

logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# 测试使用
BAIDU_API_URL = "https://qianfan.baidubce.com/v2/ai_search/chat/completions"
BAIDU_API_SIGN = os.getenv("BAIDU_API_SIGN", "")

# API配置
BOCHAAI_API_URL = "https://api.bochaai.com/v1/web-search"
BOCHAAI_API_KEY = os.getenv("BOCHAAI_API_KEY", "")

mcp = FastMCP("WebSearchService")

@mcp.tool()
async def web_search(
    query: str,
    freshness: Optional[str] = "noLimit",
    summary: Optional[bool] = False,
    include: Optional[str] = None,
    exclude: Optional[str] = None,
    count: Optional[int] = 10,
) -> dict:
    """
    使用博查AI API进行网络搜索
    Args:
        query: 搜索查询词
        freshness: 搜索指定时间范围内的网页。noLimit，不限（默认）oneDay，oneWeek，oneMonth，oneYear，- YYYY-MM-DD..YYYY-MM-DD，搜索日期范围，例如："2025-01-01..2025-04-06"- YYYY-MM-DD，搜索指定日期，例如："2025-04-06"
        summary: 是否显示文本摘要
        include: 包含的域名列表。指定搜索的网站范围。多个域名使用|或,分隔，最多不能超过20个
        exclude: 排除的域名列表。排除搜索的网站范围。多个域名使用|或,分隔，最多不能超过20个
        count: 返回结果数量，默认10
    Returns:
        dict: 搜索结果
    """
    if not BOCHAAI_API_KEY:
        return {"error": "api_key_missing", "message": "BOCHAAI_API_KEY环境变量未设置"}
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {BOCHAAI_API_KEY}"
    }
    
    payload = {
        "query": query,
        "freshness": freshness,
        "summary": summary,
        "count": count
    }
    
    # 添加可选参数
    if include:
        payload["include"] = include
    if exclude:
        payload["exclude"] = exclude
    
    try:
        response = requests.post(
            BOCHAAI_API_URL,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.code == 200:
            return response.json()
        else:
            logger.error(f"API请求失败: {response.status_code}, {response.text}")
            return {
                "error": "api_request_failed",
                "message": f"API请求失败，状态码: {response.status_code}",
                "details": response.text
            }
    
    except requests.exceptions.Timeout:
        logger.error("API请求超时")
        return {"error": "timeout", "message": "API请求超时"}
    
    except requests.exceptions.RequestException as e:
        logger.error(f"网络请求异常: {str(e)}")
        traceback.print_exc()
        return {"error": "network_error", "message": f"网络请求异常: {str(e)}"}
    
    except Exception as e:
        logger.error(f"搜索过程中出现未知错误: {str(e)}")
        traceback.print_exc()
        return {"error": "unknown_error", "message": f"搜索过程中出现未知错误: {str(e)}"}


@mcp.tool()
async def baidu_websearch(query: str) -> dict:
    """
    使用百度web search接口进行网络搜索（测试）
    Returns:
        dict: 搜索结果
    """
    try:
        # 简单的API连通性测试
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {BAIDU_API_SIGN}"
        }
        
        test_payload = {
            "messages": [
                {
                    "content": query,
                    "role": "user"
                }
            ],
            "search_filter": {
                "match": {
                    "site": [
                        "www.weather.com.cn"
                    ]
                }
            }
        }
        
        response = requests.post(
            BAIDU_API_URL,
            headers=headers,
            json=test_payload,
            timeout=30
        )
        
        return response.json()
    
    except Exception as e:
        return {
            "status": "unhealthy",
            "api_accessible": False,
            "message": f"健康检查失败: {str(e)}"
        }

if __name__ == "__main__":
    app = FastAPI()
    app.mount("/", mcp.sse_app())
    uvicorn.run(app, host="0.0.0.0", port=8003)

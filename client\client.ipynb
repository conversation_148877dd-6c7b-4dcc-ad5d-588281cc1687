{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Create server parameters for stdio connection\n", "from mcp import ClientSession, StdioServerParameters\n", "from mcp.client.stdio import stdio_client\n", "\n", "from langchain_mcp_adapters.tools import load_mcp_tools\n", "from langgraph.prebuilt import create_react_agent\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "import os"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["LLM_MODEL=\"Qwen/Qwen2.5-72B-Instruct-128K\"\n", "\n", "LLM_TEMPERATURE=0.1\n", "LLM_API_BASE=\"https://api.siliconflow.cn/v1\"\n", "#OPENAI_API_KEY=sk-8a7b594b6ec14eda83b68c2bb67500ac\n", "OPENAI_API_KEY=\"sk-jexorjtcpbtmhtnxyqcvlzbkxjhsqaqzvkrniotauxuzjlmn\"\n", "\n", "os.environ[\"LLM_MODEL\"] = LLM_MODEL\n", "os.environ[\"OPENAI_API_KEY\"] = OPENAI_API_KEY\n", "os.environ[\"LLM_API_BASE\"] = LLM_API_BASE\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["model = ChatOpenAI(\n", "        model=os.getenv(\"LLM_MODEL\"),\n", "        openai_api_key=os.getenv(\"OPENAI_API_KEY\"),\n", "        openai_api_base=os.getenv(\"LLM_API_BASE\"),\n", "    )"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# server_params = StdioServerParameters(\n", "#     command=\"python\",\n", "#     # Make sure to update to the full absolute path to your math_server.py file\n", "#     args=[\"/Users/<USER>/workspace/roardata_mcp/server/math_server.py\"],\n", "# )\n", "\n", "# async with stdio_client(server_params) as (read, write):\n", "#     async with ClientSession(read, write) as session:\n", "#         # Initialize the connection\n", "#         await session.initialize()\n", "\n", "#         # Get tools\n", "#         tools = await load_mcp_tools(session)\n", "\n", "#         # Create and run the agent\n", "#         agent = create_react_agent(model, tools)\n", "#         agent_response = await agent.ainvoke({\"messages\": \"三乘以四是多少?\"})"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["消息流程:\n", "消息 1:\n", "  类型: HumanMessage\n", "  内容: 三乘以四是多少?\n", "\n", "消息 2:\n", "  类型: AIMessage\n", "  内容: \n", "\n", "消息 3:\n", "  类型: ToolMessage\n", "  内容: 12\n", "  工具名称: multiply\n", "  工具调用ID: 0196cbe0dd4e23cea6d73eaef0458a6f\n", "\n", "消息 4:\n", "  类型: AIMessage\n", "  内容: 三乘以四的结果是12。\n", "\n", "最终结果: 三乘以四的结果是12。\n"]}], "source": ["# 结构化输出结果\n", "print(\"消息流程:\")\n", "for i, message in enumerate(agent_response[\"messages\"]):\n", "    print(f\"消息 {i+1}:\")\n", "    print(f\"  类型: {type(message).__name__}\")\n", "    print(f\"  内容: {message.content}\")\n", "    if hasattr(message, 'name') and message.name:\n", "        print(f\"  工具名称: {message.name}\")\n", "    if hasattr(message, 'tool_call_id') and message.tool_call_id:\n", "        print(f\"  工具调用ID: {message.tool_call_id}\")\n", "    print()\n", "\n", "print(\"最终结果:\", agent_response[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from langchain_mcp_adapters.client import MultiServerMCPClient"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'MultiServerMCPClient' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[43mMultiServerMCPClient\u001b[49m(\n\u001b[1;32m      2\u001b[0m     {\n\u001b[1;32m      3\u001b[0m         \u001b[38;5;66;03m# \"math\": {\u001b[39;00m\n\u001b[1;32m      4\u001b[0m         \u001b[38;5;66;03m#     \"command\": \"python\",\u001b[39;00m\n\u001b[1;32m      5\u001b[0m         \u001b[38;5;66;03m#     # Make sure to update to the full absolute path to your math_server.py file\u001b[39;00m\n\u001b[1;32m      6\u001b[0m         \u001b[38;5;66;03m#     \"args\":[\"/Users/<USER>/workspace/roardata_mcp/server/math_server.py\"],\u001b[39;00m\n\u001b[1;32m      7\u001b[0m         \u001b[38;5;66;03m#     \"transport\": \"stdio\",\u001b[39;00m\n\u001b[1;32m      8\u001b[0m         \u001b[38;5;66;03m# },\u001b[39;00m\n\u001b[1;32m      9\u001b[0m         \u001b[38;5;66;03m# \"weather\": {\u001b[39;00m\n\u001b[1;32m     10\u001b[0m         \u001b[38;5;66;03m#     # make sure you start your weather server on port 8000\u001b[39;00m\n\u001b[1;32m     11\u001b[0m         \u001b[38;5;66;03m#     \"url\": \"http://localhost:8001/sse\",\u001b[39;00m\n\u001b[1;32m     12\u001b[0m         \u001b[38;5;66;03m#     \"transport\": \"sse\",\u001b[39;00m\n\u001b[1;32m     13\u001b[0m         \u001b[38;5;66;03m# },\u001b[39;00m\n\u001b[1;32m     14\u001b[0m          \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mentity\u001b[39m\u001b[38;5;124m\"\u001b[39m: {\n\u001b[1;32m     15\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124murl\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttp://localhost:8001/sse\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     16\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtransport\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msse\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     17\u001b[0m         },\n\u001b[1;32m     18\u001b[0m     }\n\u001b[1;32m     19\u001b[0m ) \u001b[38;5;28;01mas\u001b[39;00m client:\n\u001b[1;32m     20\u001b[0m     agent \u001b[38;5;241m=\u001b[39m create_react_agent(model, client\u001b[38;5;241m.\u001b[39mget_tools())\n\u001b[1;32m     21\u001b[0m     entity_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mawait\u001b[39;00m agent\u001b[38;5;241m.\u001b[39mainvoke({\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessages\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbiz 是 MzA4MDY1NTUyMg== 最新观点\u001b[39m\u001b[38;5;124m\"\u001b[39m})\n", "\u001b[0;31mNameError\u001b[0m: name 'MultiServerMCPClient' is not defined"]}], "source": ["async with MultiServerMCPClient(\n", "    {\n", "        # \"math\": {\n", "        #     \"command\": \"python\",\n", "        #     # Make sure to update to the full absolute path to your math_server.py file\n", "        #     \"args\":[\"/Users/<USER>/workspace/roardata_mcp/server/math_server.py\"],\n", "        #     \"transport\": \"stdio\",\n", "        # },\n", "        # \"weather\": {\n", "        #     # make sure you start your weather server on port 8000\n", "        #     \"url\": \"http://localhost:8001/sse\",\n", "        #     \"transport\": \"sse\",\n", "        # },\n", "         \"entity\": {\n", "            \"url\": \"http://localhost:8001/sse\",\n", "            \"transport\": \"sse\",\n", "        },\n", "    }\n", ") as client:\n", "    agent = create_react_agent(model, client.get_tools())\n", "    entity_response = await agent.ainvoke({\"messages\": \"biz 是 MzA4MDY1NTUyMg== 最新观点\"})\n", "    print(\"数学计算消息流程:\")\n", "    for i, message in enumerate(entity_response[\"messages\"]):\n", "        print(f\"消息 {i+1}:\")\n", "        print(f\"  类型: {type(message).__name__}\")\n", "        print(f\"  内容: {message.content}\")\n", "        if hasattr(message, 'name') and message.name:\n", "            print(f\"  工具名称: {message.name}\")\n", "        if hasattr(message, 'tool_call_id') and message.tool_call_id:\n", "            print(f\"  工具调用ID: {message.tool_call_id}\")\n", "        print()\n", "\n", "    print(\"实体:\", entity_response[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数学计算消息流程:\n", "消息 1:\n", "  类型: HumanMessage\n", "  内容: biz 是 MzA4MDY1NTUyMg== 最新观点\n", "\n", "消息 2:\n", "  类型: AIMessage\n", "  内容: 为了查找与给定 `biz` 值（MzA4MDY1NTUyMg==）相关的最新观点，我们通常需要获取该 `biz` 值对应的微信文章数据。然而，提供的工具中没有直接支持通过 `biz` 值查询文章的功能。但我们可以尝试以下步骤：\n", "\n", "1. 获取所有去重的 `site_id` 和 `author` 组合。\n", "2. 检查从步骤 1 获得的结果中是否存在与 `MzA4MDY1NTUyMg==` 相对应的 `site_id` 或 `author`。\n", "3. 如果存在对应的 `site_id` 或 `author`，则根据它查询最新的微信文章。\n", "\n", "首先，让我们获取所有去重的 `site_id` 和 `author` 组合。\n", "\n", "消息 3:\n", "  类型: ToolMessage\n", "  内容: {\"accounts\": [], \"total_unique_site_ids\": 0}\n", "  工具名称: get_unique_accounts\n", "  工具调用ID: 0196cbf049b0eb66419b604211e3ffbb\n", "\n", "消息 4:\n", "  类型: AIMessage\n", "  内容: 看起来在 `pro_mcp_data_weixin` 索引中没有找到任何去重的 `site_id` 和 `author` 组合，这意味着可能没有与 `MzA4MDY1NTUyMg==` 对应的文章数据。\n", "\n", "不过，您能否提供更多上下文信息或具体文本内容？如果有特定的时间范围或关键词，我们可以尝试通过 `search_articles_by_time_and_text` 函数进行搜索。例如，如果您知道某个关键词或文章标题的一部分，我们可以使用这个信息来查找最新的相关文章。\n", "\n", "实体: 看起来在 `pro_mcp_data_weixin` 索引中没有找到任何去重的 `site_id` 和 `author` 组合，这意味着可能没有与 `MzA4MDY1NTUyMg==` 对应的文章数据。\n", "\n", "不过，您能否提供更多上下文信息或具体文本内容？如果有特定的时间范围或关键词，我们可以尝试通过 `search_articles_by_time_and_text` 函数进行搜索。例如，如果您知道某个关键词或文章标题的一部分，我们可以使用这个信息来查找最新的相关文章。\n"]}], "source": ["# 结构化输出数学计算结果\n", "# entity_response = await agent.ainvoke({\"messages\": \"几点了\"})\n", "print(\"数学计算消息流程:\")\n", "for i, message in enumerate(entity_response[\"messages\"]):\n", "    print(f\"消息 {i+1}:\")\n", "    print(f\"  类型: {type(message).__name__}\")\n", "    print(f\"  内容: {message.content}\")\n", "    if hasattr(message, 'name') and message.name:\n", "        print(f\"  工具名称: {message.name}\")\n", "    if hasattr(message, 'tool_call_id') and message.tool_call_id:\n", "        print(f\"  工具调用ID: {message.tool_call_id}\")\n", "    print()\n", "\n", "print(\"实体:\", entity_response[\"messages\"][-1].content)\n", "\n", "# # 结构化输出天气查询结果\n", "# print(\"\\n天气查询消息流程:\")\n", "# for i, message in enumerate(weather_response[\"messages\"]):\n", "#     print(f\"消息 {i+1}:\")\n", "#     print(f\"  类型: {type(message).__name__}\")\n", "#     print(f\"  内容: {message.content}\")\n", "#     if hasattr(message, 'name') and message.name:\n", "#         print(f\"  工具名称: {message.name}\")\n", "#     if hasattr(message, 'tool_call_id') and message.tool_call_id:\n", "#         print(f\"  工具调用ID: {message.tool_call_id}\")\n", "#     print()\n", "\n", "# print(\"天气查询最终结果:\", weather_response[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}
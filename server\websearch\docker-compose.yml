
services:
  websearch-mcp:
    # build: .
    image: websearch:latest
    container_name: websearch-mcp-server
    ports:
      - "8003:8003"
    environment:
      - BOCHAAI_API_KEY=BOCHAAI_API_KEY
      - BAIDU_API_SIGN=BAIDU_API_SIGN
    # env_file:
    #   - .env
    restart: unless-stopped
#     networks:
#       - mcp-network
#     volumes:
#       - ./logs:/app/logs
#     logging:
#       driver: "json-file"
#       options:
#         max-size: "10m"
#         max-file: "3"

# networks:
#   mcp-network:
#     driver: bridge

# volumes:
#   logs:

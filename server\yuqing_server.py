# yuqing_server.py
from typing import List, Optional, Dict, Any
import os # Add os import
from dotenv import load_dotenv # Add dotenv import
from mcp.server.fastmcp import FastMCP
from elasticsearch import Elasticsearch # Uncomment ES import
import requests
import traceback
import logging
import datetime # Add datetime import
import re # Add re import

logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Configure Elasticsearch connection details
ES_HOST = os.getenv("ES_HOST", "localhost") # Get ES_HOST from .env, default to localhost
ES_PORT = int(os.getenv("ES_PORT", "9200")) # Get ES_PORT from .env, default to 9200
es_client = Elasticsearch([{'host': ES_HOST, 'scheme': 'http', 'port': ES_PORT}]) # Initialize ES client and specify http scheme

# TODO: Initialize your sentence embedding model
# from sentence_transformers import SentenceTransformer
# embedding_model = SentenceTransformer('your_embedding_model_name')

mcp = FastMCP("YuqingSearchService", log_level="DEBUG", port=8001)


embedding_config = {
    "api_key": os.getenv("EMBEDDING_API_KEY", ""),
    "service_url": os.getenv("EMBEDDING_SERVICE_URL", ""),
    "embedding_name": os.getenv("EMBEDDING_MODEL_NAME", "bge-m3")
}
def get_embedding(text,embedding_config):
    access_token = embedding_config.get("api_key", "")
    service_url = embedding_config.get("service_url", "")
    model = embedding_config.get("embedding_name", "bge-m3")
    try:
        headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}
        req = {
            "input": [text],
            "model": model
        }
        embdd_response = requests.post(url=service_url, json=req, headers=headers)
        if embdd_response.status_code == 200:
            # print("embdd_response.json():", len(embdd_response.json()['data'][0]['embedding']))
            query_embedding =  embdd_response.json()['data'][0]['embedding']
        
            DB_VECTOR_DIMENSION = 1536

            # 如果生成的向量维度与数据库不匹配，进行扩充或截断
            if len(query_embedding) != DB_VECTOR_DIMENSION:
                if len(query_embedding) < DB_VECTOR_DIMENSION:
                    # 扩充向量维度
                    query_embedding = query_embedding + [0.0] * (DB_VECTOR_DIMENSION - len(query_embedding))
                    logger.info(f"向量维度已扩充至 {DB_VECTOR_DIMENSION}")
                else:
                    # 截断向量维度
                    query_embedding = query_embedding[:DB_VECTOR_DIMENSION]
                    logger.info(f"向量维度已截断至 {DB_VECTOR_DIMENSION}")



            logger.info(f"最终查询向量维度: {len(query_embedding)}")
            return query_embedding
        else:
            logger.error(f"获取embedding失败: {embdd_response.status_code}")
            return None
    except Exception as e:
        traceback.print_exc()
        logger.error(f"从ES获取数据时出错: {str(e)}")
        return None

@mcp.tool()
def search_company_info(
    credit_code: str,
    score_gte: float = 0.0,
    publish_time_gte: str = "",
    publish_time_lte: str = "",
    tendency: str = "",
    index: str = "default"
) -> Optional[Dict[str, Any]]:
    """根据企业信用代码和关键词进行检索
    
    Args:
        credit_code: 企业信用代码
        score_gte: 相关性分数阈值
        publish_time_gte: 发布时间起始
        publish_time_lte: 发布时间结束
        tendency: 倾向性关键词
        index: 索引名称
        
    Returns:
        Optional[Dict[str, Any]]: 检索结果，如果失败则返回None
    """
    base_url = "http://122.9.11.55:8084"  # 请根据实际情况修改基础URL
    
    # 构建查询条件
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "nested": {
                            "path": "correlationOrg",
                            "query": {
                                "bool": {
                                    "must": [
                                        {
                                            "term": {
                                                "correlationOrg.creditCode.keyword": {
                                                    "value": credit_code
                                                }
                                            }
                                        },
                                        {
                                            "range": {
                                                "correlationOrg.score": {
                                                    "gte": score_gte
                                                }
                                            }
                                        }
                                    ]
                                }
                            }
                        }
                    }
                ]
            }
        },
        "_source": ['site_url', 'title', 'publishtime', 'tag', 'url', "content"]
    }
    
    # 添加时间范围条件
    if publish_time_gte or publish_time_lte:
        time_range = {}
        if publish_time_gte:
            time_range["gte"] = publish_time_gte
        if publish_time_lte:
            time_range["lte"] = publish_time_lte
        if time_range:
            query["query"]["bool"]["must"].append({
                "range": {
                    "publishtime": time_range
                }
            })
    
    # 添加倾向性关键词条件
    if tendency:
        query["query"]["bool"]["must"].append({
            "match": {
                "tendency": tendency
            }
        })
    
    try:
        url = f"{base_url}/v1/api/getDataIn1358"
        response = requests.post(url, json={"index": index, "query": query})
        response.raise_for_status()
        
        data = response.json()
        if data and data.get('result') == 'ok':
            return data.get('message')
        return None
        
    except requests.RequestException as e:
        print(f"检索失败: {str(e)}")
        return None

    
@mcp.tool()
async def search_weixin_articles(
    query_text: str,
    site_id: Optional[str] = None,
    sort_by_publishtime: Optional[str] = "desc", # "asc", "desc", or None
    top_k: int = 10
) -> dict:
    """根据文本内容（通过embedding相似度）搜索微信文章。

    通过将输入文本转换为向量，在Elasticsearch的`pro_mcp_data_weixin`索引中
    进行向量相似度搜索。可以按`site_id`进行过滤，并按`publishtime`排序。

    Args:
        query_text (str): 用于向量相似度搜索的文本。
        site_id (Optional[str], optional): 要筛选的站点ID。默认为 None。
        sort_by_publishtime (Optional[str], optional): 按发布时间排序，可选值为 "asc" 或 "desc"。
                                                     默认为 "desc"。
        top_k (int, optional): 返回结果的最大数量。默认为 10。

    Returns:
        dict: 包含搜索结果 (hits) 和查询详情的字典。
              如果查询出错，则返回包含错误信息的字典。
    """
    # query_vector = get_embedding(query_text,embedding_config)

    es_query_body = {
        "size": top_k,
        "query": {
            "script_score": {
                "query": {"match_all": {}}, # Base query, can be more specific
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                    "params": {"query_vector": get_embedding(query_text, embedding_config)}
                }
            }
        }
    }

    filters = []
    if site_id:
        filters.append({"term": {"site_id": site_id}})

    if filters:
        es_query_body["query"]["script_score"]["query"] = {
            "bool": {
                "must": {"match_all": {}}, # Or keep the original query if it's not match_all
                "filter": filters
            }
        }
    
    if sort_by_publishtime and sort_by_publishtime in ["asc", "desc"]:
        es_query_body["sort"] = [
            {"publishtime": {"order": sort_by_publishtime}}
        ]
    elif "sort" in es_query_body : # if no sort_by_publishtime, ensure no sort is applied unless it's by score
        del es_query_body["sort"]


    # Execute query using es_client
    try:
        response = es_client.search(
            index="pro_mcp_data_weixin", # Make sure this index name is correct
            body=es_query_body
        )
        results = response['hits']['hits']
        return {"results": results, "query_details": es_query_body}
    except Exception as e:
        # Log the exception or handle it as needed
        print(f"Error during Elasticsearch query: {e}")
        return {
            "error": str(e),
            "message": "Error executing Elasticsearch query.",
            "query_details": es_query_body
        }

@mcp.tool()
async def get_unique_accounts() -> dict:
    """获取 pro_mcp_data_weixin 索引中所有去重的 site_id 和 author 组合。

    通过Elasticsearch聚合查询，统计并返回索引中所有唯一的`site_id`
    及其对应的唯一`author`列表。

    Args:
        None

    Returns:
        dict: 包含账户列表 (每个账户是一个包含 'site_id' 和 'author' 的字典)
              和唯一 `site_id` 总数的字典。
              如果查询出错，则返回包含错误信息的字典。
    """
    aggregation_query = {
        "size": 0,  # We don't need the hits, only aggregations
        "aggs": {
            "unique_site_ids": {
                "terms": {"field": "site_id.keyword", "size": 10000}, # Adjust size as needed
                "aggs": {
                    "unique_authors": {
                        "terms": {"field": "author.keyword", "size": 1000} # Adjust size as needed
                    }
                }
            }
        }
    }

    try:
        response = es_client.search(
            index="pro_mcp_data_weixin",
            body=aggregation_query
        )
        
        accounts = []
        site_id_buckets = response.get('aggregations', {}).get('unique_site_ids', {}).get('buckets', [])
        
        for site_bucket in site_id_buckets:
            site_id = site_bucket.get('key')
            author_buckets = site_bucket.get('unique_authors', {}).get('buckets', [])
            if not author_buckets: # If a site_id has no authors associated in this structure, still list site_id with None author
                accounts.append({
                    "site_id": site_id,
                    "author": None
                })
            else:
                for author_bucket in author_buckets:
                    author = author_bucket.get('key')
                    accounts.append({
                        "site_id": site_id,
                        "author": author
                    })
        
        return {"accounts": accounts, "total_unique_site_ids": len(site_id_buckets)}
    except Exception as e:
        logger.error(f"Error during Elasticsearch aggregation query for unique accounts: {e}")
        traceback.print_exc()
        return {
            "error": str(e),
            "message": "Error executing Elasticsearch aggregation query for unique accounts."
        }

@mcp.tool()
async def get_latest_articles_by_site_id(site_id: str, top_k: int = 10) -> dict:
    """根据指定的 site_id 查询最新的微信文章。

    从`pro_mcp_data_weixin`索引中检索特定`site_id`的最新文章，
    结果按`publishtime`降序排列。

    Args:
        site_id (str): (必需) 要查询的站点ID。
        top_k (int, optional): 返回最新文章的最大数量。默认为 10。

    Returns:
        dict: 包含搜索结果 (hits)、查询详情和查询的`site_id`的字典。
              如果查询出错，则返回包含错误信息的字典。
    """
    query_body = {
        "size": top_k,
        "query": {
            "term": {
                "site_id.keyword": site_id  # Assuming site_id is a keyword field
            }
        },
        "sort": [
            {"publishtime": {"order": "desc"}}
        ]
    }

    try:
        response = es_client.search(
            index="pro_mcp_data_weixin",
            body=query_body
        )
        results = response['hits']['hits']
        return {"results": results, "query_details": query_body, "site_id_queried": site_id}
    except Exception as e:
        logger.error(f"Error during Elasticsearch query for latest articles by site_id '{site_id}': {e}")
        traceback.print_exc()
        return {
            "error": str(e),
            "message": f"Error executing Elasticsearch query for site_id '{site_id}'.",
            "query_details": query_body
        }

@mcp.tool()
async def search_articles_by_time_and_text(
    start_time: str,
    end_time: str,
    search_text: str,
    site_id: Optional[str] = None,
    top_k: int = 10,
    sort_by_publishtime: Optional[str] = "desc"
) -> dict:
    """根据时间范围、文本内容和可选的site_id搜索微信文章。

    在`pro_mcp_data_weixin`索引中，根据指定的发布时间范围 (`start_time`
    和 `end_time`) 和文本内容 (`search_text`，匹配标题、内容、摘要)
    进行搜索。可以选择通过`site_id`过滤，并按`publishtime`排序。

    Args:
        start_time (str): (必需) 搜索的开始时间，格式为 "yyyy-MM-dd HH:mm:ss"。
        end_time (str): (必需) 搜索的结束时间，格式为 "yyyy-MM-dd HH:mm:ss"。
        search_text (str): (必需) 用于在文章标题、内容和摘要中搜索的文本。
        site_id (Optional[str], optional): (可选) 要筛选的站点ID。默认为 None。
        top_k (int, optional): 返回结果的最大数量。默认为 10。
        sort_by_publishtime (Optional[str], optional): 按发布时间排序，可选值为 "asc" 或 "desc"。
                                                     默认为 "desc"。

    Returns:
        dict: 包含搜索结果 (hits)、查询详情和接收到的参数的字典。
              如果查询出错，则返回包含错误信息的字典。
    """
    query_filters = [
        {
            "range": {
                "publishtime": {
                    "gte": start_time,
                    "lte": end_time,
                    "format": "yyyy-MM-dd HH:mm:ss"
                }
            }
        }
    ]

    if site_id:
        query_filters.append({"term": {"site_id.keyword": site_id}})

    query_body = {
        "size": top_k,
        "query": {
            "bool": {
                "must": [
                    {
                        "multi_match": {
                            "query": search_text,
                            "fields": ["title", "content", "summary"] # Adjust fields as needed
                        }
                    }
                ],
                "filter": query_filters
            }
        }
    }

    if sort_by_publishtime and sort_by_publishtime in ["asc", "desc"]:
        query_body["sort"] = [
            {"publishtime": {"order": sort_by_publishtime}}
        ]
    
    try:
        response = es_client.search(
            index="pro_mcp_data_weixin",
            body=query_body
        )
        results = response['hits']['hits']
        return {
            "results": results,
            "query_details": query_body,
            "params_received": {
                "start_time": start_time,
                "end_time": end_time,
                "search_text": search_text,
                "site_id": site_id,
                "top_k": top_k,
                "sort_by_publishtime": sort_by_publishtime
            }
        }
    except Exception as e:
        logger.error(f"Error during Elasticsearch query by time and text: {e}")
        traceback.print_exc()
        return {
            "error": str(e),
            "message": "Error executing Elasticsearch query by time and text.",
            "query_details": query_body
        }

@mcp.tool()
async def get_current_time() -> dict:
    """获取当前的日期和时间。

    Returns:
        dict: 包含当前时间 (格式: "yyyy-MM-dd HH:mm:ss") 的字典。
    """
    now = datetime.datetime.now()
    formatted_time = now.strftime("%Y-%m-%d %H:%M:%S")
    return {"current_time": formatted_time}

@mcp.tool()
async def find_biz_by_author_name(author_name: str) -> dict:
    """根据公众号名称（作者）查询并从其文章URL中提取biz。

    在`pro_mcp_data_weixin`索引中搜索与指定`author_name`匹配的文章，
    并尝试从文章的URL中解析出`__biz`参数。

    Args:
        author_name (str): (必需) 要查询的公众号准确名称（作者名）。

    Returns:
        dict: 如果成功，返回包含 'author_name' 和 'biz' 的字典。
              如果未找到或无法解析biz，则返回包含错误/提示信息的字典。
    """
    query_body = {
        "size": 1,
        "query": {
            "term": {
                "author.keyword": author_name  # Assuming 'author' is mapped as keyword
            }
        },
        "_source": ["url"]
    }

    try:
        response = await es_client.search( # Use await for async es_client call
            index="pro_mcp_data_weixin",
            body=query_body
        )
        
        hits = response.get('hits', {}).get('hits', [])
        if not hits:
            return {"error": "not_found", "message": f"未找到作者 '{author_name}' 的相关文章。", "author_name": author_name}

        source_data = hits[0].get('_source', {})
        article_url = source_data.get('url')

        if not article_url:
            return {"error": "no_url", "message": f"作者 '{author_name}' 的文章未包含URL。", "author_name": author_name}

        # Try to extract biz from URL
        # Example URL: https://mp.weixin.qq.com/s?__biz=MzAxMTE2NDY1OA==&mid=...
        match = re.search(r"__biz=([^&]+)", article_url)
        if match:
            biz_value = match.group(1)
            return {"author_name": author_name, "biz": biz_value}
        else:
            return {"error": "biz_not_found_in_url", "message": f"在作者 '{author_name}' 的文章URL中未找到biz。", "url_checked": article_url, "author_name": author_name}

    except Exception as e:
        logger.error(f"通过作者名 '{author_name}' 查询biz时出错: {e}")
        traceback.print_exc()
        return {
            "error": "query_exception",
            "message": f"查询作者 '{author_name}' 的biz时发生错误: {str(e)}",
            "author_name": author_name
        }

if __name__ == "__main__":
    mcp.run(transport="sse")
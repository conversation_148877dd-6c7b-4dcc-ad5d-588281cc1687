from typing import List, Optional, Dict, Any
import os
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from elasticsearch import Elasticsearch
import requests
import traceback
import logging
import datetime

logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# ES连接配置
ES_HOST = os.getenv("ES_HOST", "localhost")
ES_PORT = int(os.getenv("ES_PORT", "9200"))
es_client = Elasticsearch([{'host': ES_HOST, 'scheme': 'http', 'port': ES_PORT}])

mcp = FastMCP("PolicyRadarService", log_level="DEBUG", port=8002)

embedding_config = {
    "api_key": os.getenv("EMBEDDING_API_KEY", ""),
    "service_url": os.getenv("EMBEDDING_SERVICE_URL", ""),
    "embedding_name": os.getenv("EMBEDDING_MODEL_NAME", "bge-m3")
}

def get_embedding(text, embedding_config):
    access_token = embedding_config.get("api_key", "")
    service_url = embedding_config.get("service_url", "")
    model = embedding_config.get("embedding_name", "bge-m3")
    try:
        headers = {'Content-Type': 'application/json', 'Authorization': f'{access_token}'}
        req = {
            "input": [text],
            "model": model
        }
        embdd_response = requests.post(url=service_url, json=req, headers=headers)
        if embdd_response.status_code == 200:
            query_embedding = embdd_response.json()['data'][0]['embedding']
            DB_VECTOR_DIMENSION = 1536
            if len(query_embedding) != DB_VECTOR_DIMENSION:
                if len(query_embedding) < DB_VECTOR_DIMENSION:
                    query_embedding = query_embedding + [0.0] * (DB_VECTOR_DIMENSION - len(query_embedding))
                else:
                    query_embedding = query_embedding[:DB_VECTOR_DIMENSION]
            return query_embedding
        else:
            logger.error(f"获取embedding失败: {embdd_response.status_code}")
            return None
    except Exception as e:
        traceback.print_exc()
        logger.error(f"获取embedding时出错: {str(e)}")
        return None

INDEX_NAME = "pro_mcp_data_zhengce_v2"

@mcp.tool()
async def search_policy_by_keyword(
    keyword: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    area: Optional[str] = None,
    department: Optional[str] = None,
    top_k: int = 10
) -> dict:
    """
    关键词检索政策，支持时间、地域、发布部门等过滤。
    Args:
        keyword: 检索关键词
        start_time: 公布时间起（格式yyyy-MM-dd HH:mm:ss）
        end_time: 公布时间止
        area: 地域
        department: 发布部门
        top_k: 返回数量
    Returns:
        dict: 检索结果
    """
    must_clauses = [
        {
            "multi_match": {
                "query": keyword,
                "fields": [
                    "title", "content", "summary", "laws_title", "laws_brief", "policy_theme.text","area","laws_approving_authority"
                ]
            }
        }
    ]
    filter_clauses = []
    if start_time or end_time:
        time_range = {}
        if start_time:
            time_range["gte"] = start_time
        if end_time:
            time_range["lte"] = end_time
        filter_clauses.append({
            "range": {
                "laws_public_time": {
                    **time_range,
                    "format": "yyyy-MM-dd HH:mm:ss"
                }
            }
        })
    if area:
        filter_clauses.append({"term": {"area": area}})
    if department:
        filter_clauses.append({"match": {"laws_approving_authority": department}})
    query_body = {
        "size": top_k,
        "query": {
            "bool": {
                "must": must_clauses,
                "filter": filter_clauses
            }
        }
    }
    try:
        response = es_client.search(index=INDEX_NAME, body=query_body)
        results = response['hits']['hits']
        return {"results": results, "query_details": query_body}
    except Exception as e:
        logger.error(f"政策关键词检索出错: {e}")
        traceback.print_exc()
        return {"error": str(e), "message": "政策关键词检索异常", "query_details": query_body}

@mcp.tool()
async def search_policy_by_embedding(
    query_text: str,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None,
    area: Optional[str] = None,
    department: Optional[str] = None,
    top_k: int = 10
) -> dict:
    """
    相似度检索政策，支持时间、地域、发布部门等过滤。
    Args:
        query_text: 检索文本
        start_time: 公布时间起（格式yyyy-MM-dd HH:mm:ss）
        end_time: 公布时间止
        area: 地域
        department: 发布部门
        top_k: 返回数量
    Returns:
        dict: 检索结果
    """
    query_vector = get_embedding(query_text, embedding_config)
    if not query_vector:
        return {"error": "embedding_failed", "message": "获取embedding失败"}
    filter_clauses = []
    if start_time or end_time:
        time_range = {}
        if start_time:
            time_range["gte"] = start_time
        if end_time:
            time_range["lte"] = end_time
        filter_clauses.append({
            "range": {
                "laws_public_time": {
                    **time_range,
                    "format": "yyyy-MM-dd HH:mm:ss"
                }
            }
        })
    if area:
        filter_clauses.append({"term": {"area": area}})
    if department:
        filter_clauses.append({"match": {"laws_approving_authority": department}})
    query_body = {
        "size": top_k,
        "query": {
            "script_score": {
                "query": {
                    "bool": {
                        "must": {"match_all": {}},
                        "filter": filter_clauses
                    }
                },
                "script": {
                    "source": "cosineSimilarity(params.query_vector, 'embedding') + 1.0",
                    "params": {"query_vector": query_vector}
                }
            }
        }
    }
    try:
        response = es_client.search(index=INDEX_NAME, body=query_body)
        results = response['hits']['hits']
        return {"results": results, "query_details": query_body}
    except Exception as e:
        logger.error(f"政策相似度检索出错: {e}")
        traceback.print_exc()
        return {"error": str(e), "message": "政策相似度检索异常", "query_details": query_body}

@mcp.tool()
async def get_current_time() -> dict:
    now = datetime.datetime.now()
    formatted_time = now.strftime("%Y-%m-%d %H:%M:%S")
    return {"current_time": formatted_time}

if __name__ == "__main__":
    mcp.run(transport="sse")
